<?php

namespace App\Http\Resources\Billing\Reports;

use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use Illuminate\Support\Number;

class OverdueInvoicesReportResource extends BaseJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'company_id'    => $this->company_id,
            'company_name'  => $this->company_name,
            'invoice_id'    => $this->invoice_id,
            'amount'        => Number::currency($this->amount / 100),
            'issue_date'    => $this->issue_date ? CarbonHelper::parse($this->issue_date)->toFormat() : null,
            'due_date'      => $this->due_date ? CarbonHelper::parse($this->due_date)->toFormat() : null,
            'paid_date'     => $this->paid_date ? CarbonHelper::parse($this->paid_date)->toFormat() : null,
            'days_overdue'  => $this->days_overdue,
        ];
    }
}
