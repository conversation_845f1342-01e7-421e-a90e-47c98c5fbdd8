<?php

namespace App\Http\Requests\Billing\Reports;

use App\Enums\PermissionType;
use App\Models\User;
use App\Rules\OrderFormat;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class GetOverdueInvoicesReportRequest extends FormRequest
{
    const string FIELD_PAGE                                 = 'page';
    const string FIELD_PER_PAGE                             = 'per_page';
    const string FIELD_ACCOUNT_MANAGER_USER_ID              = 'account_manager_user_id';
    const string FIELD_SUCCESS_MANAGER_USER_ID              = 'success_manager_user_id';
    const string FIELD_INDUSTRY_ID                          = 'industry_id';
    const string FIELD_COMPANY_ID                           = 'company_id';
    const string FIELD_BUSINESS_DEVELOPMENT_MANAGER_USER_ID = 'business_development_manager_user_id';
    const string FIELD_SORT_BY                              = 'sort_by';
    const string FIELD_ALL                                  = 'all';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::BILLING_REPORTS_VIEW->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'page'                                           => 'numeric',
            'per_page'                                       => 'numeric',
            self::FIELD_ACCOUNT_MANAGER_USER_ID              => 'numeric',
            self::FIELD_SUCCESS_MANAGER_USER_ID              => 'numeric',
            self::FIELD_BUSINESS_DEVELOPMENT_MANAGER_USER_ID => 'numeric',
            self::FIELD_INDUSTRY_ID                          => 'array',
            self::FIELD_INDUSTRY_ID . '.*'                   => 'numeric',
            self::FIELD_COMPANY_ID                           => 'numeric',
            self::FIELD_SORT_BY                              => ['sometimes', 'array'],
            self::FIELD_SORT_BY . '.*'                       => ['required', 'string', new OrderFormat()],
            self::FIELD_ALL                                  => 'bool',
        ];
    }
}
