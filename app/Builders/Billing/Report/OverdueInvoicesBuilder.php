<?php

namespace App\Builders\Billing\Report;

use App\Enums\Billing\InvoiceStates;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\Billing\InvoiceTransaction;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class OverdueInvoicesBuilder extends BaseInvoiceReportBuilder
{
    protected array $sortColumnsMap = [
        'company_id'    => Invoice::TABLE . '.' . Invoice::FIELD_COMPANY_ID,
        'company_name'  => Company::TABLE . '.' . Company::FIELD_NAME,
        'invoice_id'    => Invoice::TABLE . '.' . Invoice::FIELD_ID,
        'amount'        => InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_VALUE,
        'issue_date'    => Invoice::TABLE . '.' . Invoice::FIELD_ISSUE_AT,
        'due_date'      => Invoice::TABLE . '.' . Invoice::FIELD_DUE_AT,
        'paid_date'     => 'paid_date',
        'days_overdue'  => 'days_overdue',
    ];

    /**
     * @param Builder $query
     */
    private function __construct(Builder $query)
    {
        parent::__construct($query);
    }

    /**
     * @return self
     */
    public static function query(): OverdueInvoicesBuilder
    {
        $query = InvoiceSnapshot::mostRecentByInvoice()
            ->select([
                Invoice::TABLE . '.' . Invoice::FIELD_ID . ' as invoice_id',
                Invoice::TABLE . '.' . Invoice::FIELD_COMPANY_ID . ' as company_id',
                Company::TABLE . '.' . Company::FIELD_NAME . ' as company_name',
                InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_VALUE . ' as amount',
                Invoice::TABLE . '.' . Invoice::FIELD_ISSUE_AT . ' as issue_date',
                Invoice::TABLE . '.' . Invoice::FIELD_DUE_AT . ' as due_date',
                DB::raw('(SELECT MAX(it.date) FROM ' . InvoiceTransaction::TABLE . ' it WHERE it.invoice_uuid = ' . Invoice::TABLE . '.' . Invoice::FIELD_UUID . ' AND it.type = "payment" AND it.scenario = "won") as paid_date'),
                DB::raw('DATEDIFF(NOW(), ' . Invoice::TABLE . '.' . Invoice::FIELD_DUE_AT . ') as days_overdue'),
            ])
            ->join(Invoice::TABLE, Invoice::TABLE . '.' . Invoice::FIELD_ID, InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_INVOICE_ID)
            ->join(
                DatabaseHelperService::database() . '.' . Company::TABLE,
                Company::TABLE . '.' . Company::FIELD_ID,
                Invoice::TABLE . '.' . Invoice::FIELD_COMPANY_ID
            )
            ->where(Invoice::TABLE . '.' . Invoice::FIELD_DUE_AT, '<', now())
            ->whereDoesntHave(InvoiceSnapshot::RELATION_INVOICE, function (Builder $query) {
                $query->whereIn(Invoice::FIELD_STATUS, [
                    InvoiceStates::DRAFT->value,
                    InvoiceStates::VOIDED->value,
                    InvoiceStates::DELETED->value,
                    InvoiceStates::WRITTEN_OFF->value
                ]);
            });

        return new self($query);
    }

    /**
     * @return $this
     */
    public function joinCompany(): self
    {
        // Company is already joined in the base query
        return $this;
    }
}
