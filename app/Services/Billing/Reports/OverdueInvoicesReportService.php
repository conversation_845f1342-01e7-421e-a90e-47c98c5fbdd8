<?php

namespace App\Services\Billing\Reports;

use App\Builders\Billing\Report\OverdueInvoicesBuilder;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

class OverdueInvoicesReportService
{
    /**
     * @param User|null $user
     * @param int|null $customerSuccessManagerUserId
     * @param int|null $accountManagerUserId
     * @param int|null $businessDevelopmentManagerUserId
     * @param array|null $industryIds
     * @param int|null $companyId
     * @param array|null $sortBy
     *
     * @return Builder
     */
    public function getReport(
        ?User $user = null,
        ?int $customerSuccessManagerUserId = null,
        ?int $accountManagerUserId = null,
        ?int $businessDevelopmentManagerUserId = null,
        ?array $industryIds = [],
        ?int $companyId = null,
        ?array $sortBy = []
    ): Builder
    {
        return OverdueInvoicesBuilder::query()
            ->forCompanyUserRelationship(
                customerSuccessManagerUserId: $customerSuccessManagerUserId,
                accountManagerUserId: $accountManagerUserId,
                businessDevelopmentManagerUserId: $businessDevelopmentManagerUserId,
            )
            ->forCompanyId(companyId: $companyId)
            ->forIndustry(industryId: $industryIds)
            ->filterByRole(
                user: $user
            )
            ->sortBy($sortBy)
            ->getQuery();
    }
}
