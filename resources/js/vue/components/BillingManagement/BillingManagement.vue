<template>
    <div class="main-layout font-body">
        <div class="w-full">
            <div v-if="!loading" class="w-full flex-auto relative"
                 :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <div :class="[darkMode ? 'text-white' : 'text-slate-900']">
                    <div class="flex flex-col px-5 pt-5 border-b mb-5 gap-4"
                         :class="{'bg-light-module': !darkMode, 'bg-dark-module': darkMode}">
                        <a href="/"
                           class="text-base text-primary-500 font-medium pb-0 leading-none mr-5 mb-4 inline-flex items-center pl-4">
                            <svg class="mr-2" width="7" height="12" viewBox="0 0 7 12" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M6.70711 11.7071C6.31658 12.0976 5.68342 12.0976 5.29289 11.7071L0.292894 6.70711C-0.0976305 6.31658 -0.0976304 5.68342 0.292894 5.29289L5.29289 0.292893C5.68342 -0.0976316 6.31658 -0.0976315 6.70711 0.292893C7.09763 0.683417 7.09763 1.31658 6.70711 1.70711L2.41421 6L6.70711 10.2929C7.09763 10.6834 7.09763 11.3166 6.70711 11.7071Z"
                                      fill="#0081FF"/>
                            </svg>
                            Back
                        </a>
                        <div class="flex items-center justify-between flex-wrap pl-4">
                            <h3 class="text-xl font-medium pb-0 leading-none mr-5"
                                :class="[darkMode ? 'text-primary-500' : '']">Billing</h3>
                        </div>
                        <div class="flex justify-between">
                            <tab
                                :dark-mode="darkMode"
                                :tabs="tabTitles"
                                @selected="selectTab"
                                tab-style="fit"
                                background-color="light"
                                :tab-type="'Normal'"
                            />
                        </div>
                    </div>
                    <div class="mx-10 border rounded-md flex flex-col flex-grow p-3"
                         :class="[darkMode ? 'bg-dark-module border-dark-border text-slate-200' : 'bg-light-module']">
                        <component :is="currentTabComponent" :dark-mode="darkMode"/>
                    </div>
                </div>
            </div>
            <loading-spinner v-else/>
        </div>
        <view-create-invoice-modal
            @invoice-created-updated="handleInvoiceUpdatedOrCreated"
            @close="showInvoiceModal = false"
            v-if="showInvoiceModal"
            :dark-mode="darkMode"
        />
        <display-toast-notifications />
        <AlertsContainer
            v-if="alertActive"
            :alert-type="alertType"
            :text="alertText"
            :dark-mode="darkMode"
        />
    </div>
</template>
<script>
import Tab from "../Shared/components/Tab.vue";
import {markRaw} from "vue";
import OverviewTab from "./Tabs/OverviewTab.vue";
import InvoicesTab from "./Tabs/InvoicesTab.vue";
import InvoiceActionRequestTab from "./Tabs/InvoceActionRequest/InvoiceActionRequestTab.vue";
import ReceivableInvoicesReport from "./Tabs/Reports/ReceivableInvoicesReport.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import {PERMISSIONS, ROLES, useRolesPermissions} from "../../../stores/roles-permissions.store";
import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../mixins/alerts-mixin";
import ViewCreateInvoiceModal from "../Billing/ViewCreateInvoiceModal.vue";
import useQueryParams from "../../../composables/useQueryParams";
import BillingProfilesTab from "./Tabs/BillingProfilesTab.vue";
import InvoiceTransactionsTab from "./Tabs/InvoiceTransactionsTab.vue";
import InvoiceRefundsTab from "./Tabs/InvoiceRefundsTab.vue";
import InvoiceCollectionsTab from "./Tabs/InvoiceCollectionsTab.vue";
import InvoiceChargebacksTab from "./Tabs/InvoiceChargebacksTab.vue";
import RevenueReport from "./Tabs/Reports/RevenueReport.vue";
import AgedReport from "./Tabs/Reports/AgedReport.vue";
import InvoicesBalanceReport from "./Tabs/Reports/InvoicesBalanceReport.vue";
import CreditsMovementReport from "./Tabs/Reports/CreditsMovementReport.vue";
import CreditsOutstandingReport from "./Tabs/Reports/CreditsOutstandingReport.vue";
import InvoiceWriteOffsTab from "./Tabs/InvoiceWriteOffsTab.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import DisplayToastNotifications from "../Billing/components/ToastNotification/DisplayToastNotifications.vue";
import CompaniesOverviewReport from "./Tabs/Reports/CompaniesOverviewReport.vue";
import OverdueInvoicesReport from "./Tabs/Reports/OverdueInvoicesReport.vue";

const DEFAULT_SELECTED_TAB = 'Invoices'

export default {
    name: "BillingManagement",
    components: {
        DisplayToastNotifications,
        LoadingSpinner,
        ViewCreateInvoiceModal,
        AlertsContainer,
        CustomButton,
        Tab,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    mixins: [AlertsMixin],
    created() {
        this.loading = true
        const {tab = DEFAULT_SELECTED_TAB} = this.queryParamsHelper.getCurrentParams()
        this.setSelectedTab(tab)
    },
    data() {
        return {
            loading: false,
            permissionStore: useRolesPermissions(),
            tabs: [
                {
                    name: 'Overview', component: markRaw(OverviewTab), financeOwnerOnly: true, current: false
                },
                {
                    name: DEFAULT_SELECTED_TAB, current: false, component: markRaw(InvoicesTab)
                },
                {
                    name: "Action Requests", component: markRaw(InvoiceActionRequestTab), financeOwnerOnly: false, current: false
                },
                {
                    name: "Billing Profiles", component: markRaw(BillingProfilesTab), current: false
                },
                {
                    name: "Accounts Receivable Report", component: markRaw(ReceivableInvoicesReport), current: false
                },
                {
                    name: "Revenue Report", component: markRaw(RevenueReport), financeOwnerOnly: true, current: false
                },
                {
                    name: "Aged Report", component: markRaw(AgedReport), current: false
                },
                {
                    name: "Overdue Invoices Report", component: markRaw(OverdueInvoicesReport), current: false
                },
                {
                    name: "Balance Report", component: markRaw(InvoicesBalanceReport), financeOwnerOnly: true, current: false
                },
                {
                    name: "Transactions", component: markRaw(InvoiceTransactionsTab), financeOwnerOnly: true, current: false
                },
                {
                    name: "Refunds", component: markRaw(InvoiceRefundsTab), financeOwnerOnly: true, current: false
                },
                {
                    name: "Collections", component: markRaw(InvoiceCollectionsTab), financeOwnerOnly: true, current: false
                },
                {
                    name: "Write Offs", component: markRaw(InvoiceWriteOffsTab), financeOwnerOnly: true, current: false
                },
                {
                    name: "Chargebacks", component: markRaw(InvoiceChargebacksTab), financeOwnerOnly: true, current: false
                },
                {
                    name: "Credit Movement Report", component: markRaw(CreditsMovementReport),
                    current: false
                },
                {
                    name: "Company Overview Report", component: markRaw(CompaniesOverviewReport),
                    financeOwnerOnly: true,
                    current: false
                },
                {
                    name: "Credits Outstanding Report", component: markRaw(CreditsOutstandingReport), current: false
                },
            ],
            selectedTab: null,
            showInvoiceModal: false,
            queryParamsHelper: useQueryParams()
        }
    },
    computed: {
        tabTitles() {
            return this.tabs.filter(tab => tab?.financeOwnerOnly === this.isFinanceOwner || !tab?.financeOwnerOnly);
        },
        currentTabComponent() {
            return this.tabTitles.find(e => e.current)?.component
        },
        isFinanceOwner() {
            return this.permissionStore.hasRole(ROLES.FINANCE_OWNER);
        }
    },
    methods: {
        changeToSelectedTab() {
            this.tabs.forEach(e => {
                e.current = e.name === this.selectedTab
            })
        },
        setSelectedTab(tab) {
            if (this.tabs.length === 0) return
            this.selectedTab = tab

            if (!this.tabs.find(e => e.name === this.selectedTab)) {
                this.selectedTab = this.tabs[0].name
            }

            this.changeToSelectedTab()
        },
        selectTab(tab) {
            this.queryParamsHelper.setQueryParamsOnCurrentUrl({tab})
            this.setSelectedTab(tab)
        },
        handleInvoiceUpdatedOrCreated(invoiceUuid, message) {
            this.showAlert('success', message + invoiceUuid);
            this.showInvoiceModal = false;
        }
    },
    watch: {
        'permissionStore.initialized'(val){
            if (val) {
                this.loading = false
            }
        }
    }
}
</script>
